"""
Quotation service module.
This module provides business logic for quotations.
"""
from typing import List, Dict, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta, timezone
from marshmallow import ValidationError
from app.repositories.quotation_repository import QuotationRepository, QuotationItemRepository
from app.repositories.customer_repository import CustomerRepository
from app.repositories.product_repository import ProductRepository
from app.repositories.document_repository import DocumentRepository
from app.schemas.quotation_schema import quotation_schema, quotations_schema
from app import db

# Configure logging
logger = logging.getLogger(__name__)

class QuotationService:
    """Service for quotation-related operations."""

    def __init__(self):
        """Initialize the service with repositories."""
        self.quotation_repo = QuotationRepository()
        self.item_repo = QuotationItemRepository()
        self.customer_repo = CustomerRepository()
        self.product_repo = ProductRepository()
        self.document_repo = DocumentRepository()

    def get_all_quotations(self, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        """
        Get all quotations with pagination.

        Args:
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of quotations list and total count
        """
        quotations, total = self.quotation_repo.get_all(page, per_page)
        return [quotation.to_dict() for quotation in quotations], total

    def get_quotation_by_id(self, quotation_id: int) -> Dict:
        """
        Get a quotation by ID.

        Args:
            quotation_id: Quotation ID

        Returns:
            Quotation data

        Raises:
            Exception: If quotation not found
        """
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            raise Exception("Quotation not found")
        return quotation.to_dict()

    def get_quotations_by_customer(self, customer_id: int, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        """
        Get quotations for a customer with pagination.

        Args:
            customer_id: Customer ID
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of quotations list and total count
        """
        quotations, total = self.quotation_repo.get_by_customer(customer_id, page, per_page)
        return [quotation.to_dict() for quotation in quotations], total

    def search_quotations(self, search_term: str, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        """
        Search quotations by title, quotation number, or customer name.

        Args:
            search_term: Search term
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of quotations list and total count
        """
        quotations, total = self.quotation_repo.search(search_term, page, per_page)
        return [quotation.to_dict() for quotation in quotations], total

    def create_quotation(self, quotation_data: Dict) -> Dict:
        """
        Create a new quotation.

        Args:
            quotation_data: Quotation data

        Returns:
            Created quotation data
        """
        # Validate with schema
        errors = quotation_schema.validate(quotation_data)
        if errors:
            raise Exception(f"Validation failed: {errors}")

        # Check if customer exists
        customer = self.customer_repo.get_by_id(quotation_data["customer_id"])
        if not customer:
            raise Exception("Customer not found")

        # Generate quotation number if not provided
        if not quotation_data.get("quotation_number"):
            quotation_data["quotation_number"] = self.quotation_repo.get_next_quotation_number()

        # Set default valid until date (30 days from now) if not provided
        if not quotation_data.get("valid_until"):
            quotation_data["valid_until"] = datetime.now(timezone.utc) + timedelta(days=30)

        # Set default status if not provided
        if not quotation_data.get("status"):
            quotation_data["status"] = "concept"

        # Create quotation
        quotation = self.quotation_repo.create(quotation_data)

        # Create a document for the quotation
        try:
            logger.info(f"Creating document for new quotation {quotation.id}")
            document = self.generate_quotation_pdf(quotation.id)

            # Link the document to the quotation
            quotation.document_id = document["id"]
            logger.info(f"Linked document {document['id']} to quotation {quotation.id}")
            db.session.commit()
        except Exception as e:
            logger.error(f"Failed to create document for quotation {quotation.id}: {str(e)}")
            # Continue even if document creation fails

        return quotation.to_dict()

    def update_quotation(self, quotation_id: int, quotation_data: Dict) -> Dict:
        """
        Update a quotation.

        Args:
            quotation_id: Quotation ID
            quotation_data: Updated quotation data

        Returns:
            Updated quotation data

        Raises:
            Exception: If quotation not found or validation fails
        """
        # Get quotation
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            raise Exception("Quotation not found")

        # Create a copy of the data to avoid modifying the original
        data_to_validate = quotation_data.copy()

        # Filter out read-only fields that should not be validated
        read_only_fields = [
            'created_at', 'updated_at', 'customer_name', 'created_by_name',
            'subtotal', 'discount_amount', 'total_excl_vat', 'vat_amount',
            'total_incl_vat', 'items'
        ]

        for field in read_only_fields:
            if field in data_to_validate:
                data_to_validate.pop(field)

        # Log the data we're going to validate
        logger.info(f"Validating data for quotation {quotation_id}: {data_to_validate}")

        # Add required fields from the existing quotation if they're not in the update data
        if "customer_id" not in data_to_validate:
            data_to_validate["customer_id"] = quotation.customer_id

        if "created_by" not in data_to_validate:
            data_to_validate["created_by"] = quotation.created_by

        if "title" not in data_to_validate:
            data_to_validate["title"] = quotation.title

        # Validate with schema
        errors = quotation_schema.validate(data_to_validate)
        if errors:
            logger.error(f"Validation failed for quotation {quotation_id}: {errors}")
            raise ValidationError(errors)

        # Check if customer exists if customer_id is provided
        if "customer_id" in quotation_data:
            customer = self.customer_repo.get_by_id(quotation_data["customer_id"])
            if not customer:
                raise Exception("Customer not found")

        try:
            # Update quotation
            updated_quotation = self.quotation_repo.update(quotation, quotation_data)
            return updated_quotation.to_dict()
        except Exception as e:
            logger.error(f"Error updating quotation {quotation_id}: {str(e)}")
            raise Exception(f"Failed to update quotation: {str(e)}")

    def delete_quotation(self, quotation_id: int) -> bool:
        """
        Delete a quotation.

        Args:
            quotation_id: Quotation ID

        Returns:
            True if deleted, False if not found
        """
        return self.quotation_repo.delete(quotation_id)

    def add_item_to_quotation(self, quotation_id: int, item_data: Dict) -> Dict:
        """
        Add an item to a quotation.

        Args:
            quotation_id: Quotation ID
            item_data: Item data

        Returns:
            Added item data

        Raises:
            Exception: If quotation not found or validation fails
        """
        # Get quotation
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            raise Exception("Quotation not found")

        # Set quotation ID in item data
        item_data["quotation_id"] = quotation_id

        # If product_id is provided, get product details
        if item_data.get("product_id"):
            product = self.product_repo.get_by_id(item_data["product_id"])
            if not product:
                raise Exception("Product not found")

            # Set product details if not provided
            if not item_data.get("description"):
                item_data["description"] = product.name

            if not item_data.get("unit_price"):
                item_data["unit_price"] = product.gross_price

        # Create item
        item = self.item_repo.create(item_data)
        return item.to_dict()

    def update_item(self, item_id: int, item_data: Dict) -> Dict:
        """
        Update a quotation item.

        Args:
            item_id: Item ID
            item_data: Updated item data

        Returns:
            Updated item data

        Raises:
            Exception: If item not found or validation fails
        """
        # Get item
        item = self.item_repo.get_by_id(item_id)
        if not item:
            raise Exception("Quotation item not found")

        # Update item
        updated_item = self.item_repo.update(item, item_data)
        return updated_item.to_dict()

    def delete_item(self, item_id: int) -> bool:
        """
        Delete a quotation item.

        Args:
            item_id: Item ID

        Returns:
            True if deleted, False if not found
        """
        return self.item_repo.delete(item_id)

    def generate_quotation_pdf(self, quotation_id: int) -> Dict:
        """
        Generate a PDF for a quotation and save it as a document.

        Args:
            quotation_id: Quotation ID

        Returns:
            Document data

        Raises:
            Exception: If quotation not found or PDF generation fails
        """
        # Debug: Log start of PDF generation
        logger.info(f"Starting PDF generation for quotation {quotation_id}")

        # Get quotation
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            raise Exception("Quotation not found")

        # Debug: Log quotation details
        logger.info(f"Quotation found: {quotation.id}, valid_until: {quotation.valid_until}, type: {type(quotation.valid_until)}")
        if quotation.valid_until:
            logger.info(f"valid_until tzinfo: {quotation.valid_until.tzinfo}")

        # Import document repository here to avoid circular imports
        from app.repositories.document_repository import DocumentRepository
        document_repo = DocumentRepository()

        # Check if a document already exists for this quotation
        if quotation.document_id:
            existing_document = document_repo.get_by_id(quotation.document_id)
            if existing_document:
                return existing_document.to_dict()

        # Get customer data
        customer = self.customer_repo.get_by_id(quotation.customer_id)
        if not customer:
            raise Exception("Customer not found")

        # Generate PDF using ReportLab
        import uuid
        import os
        import io
        from datetime import datetime
        from reportlab.lib.pagesizes import A4
        from reportlab.lib import colors
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from app.utils.firebase import upload_bytes_to_storage

        # Generate a unique filename for Firebase Storage
        filename = f"quotation_{quotation.id}_{uuid.uuid4()}.pdf"

        # Create PDF in memory
        buffer = io.BytesIO()

        # Helper function to format prices
        def format_price(value):
            if value is None:
                return "0,00"
            # Rond af op 2 decimalen voordat we formatteren
            rounded_value = round(float(value), 2)
            return f"{rounded_value:.2f}".replace('.', ',')

        # Helper function to format dates
        def format_date(value):
            if not value:
                return ""
            try:
                # Debug: Log the input value
                logger.info(f"format_date input: {value}, type: {type(value)}")
                if isinstance(value, str):
                    # Handle ISO format strings
                    if 'Z' in value:
                        value = value.replace('Z', '+00:00')
                    if '+' not in value and '-' in value and 'T' in value:
                        # Add timezone info if missing
                        value = value + '+00:00'
                    dt = datetime.fromisoformat(value)
                    logger.info(f"Parsed string to datetime: {dt}, tzinfo: {dt.tzinfo}")
                elif isinstance(value, datetime):
                    # Ensure datetime has timezone info
                    if value.tzinfo is None:
                        dt = value.replace(tzinfo=timezone.utc)
                        logger.info(f"Added timezone to datetime: {dt}, tzinfo: {dt.tzinfo}")
                    else:
                        dt = value
                        logger.info(f"Using datetime with existing timezone: {dt}, tzinfo: {dt.tzinfo}")
                else:
                    # For other types, convert to string
                    logger.info(f"Converting non-datetime value to string: {value}")
                    return str(value)
                formatted = dt.strftime('%d-%m-%Y')
                logger.info(f"Formatted date: {formatted}")
                return formatted
            except Exception as e:
                logger.error(f"Error formatting date {value}: {str(e)}")
                return str(value)

        # Convert quotation to dict to avoid timezone issues
        quotation_dict = quotation.to_dict()

        # Create the PDF document in memory
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )

        # Get styles
        styles = getSampleStyleSheet()
        styles.add(ParagraphStyle(
            name='Center',
            parent=styles['Heading1'],
            alignment=1,  # 0=left, 1=center, 2=right
        ))

        # Create content elements
        elements = []

        # Add company header (logo will be handled separately in Firebase Storage)
        # For now, we'll use text header
        elements.append(Paragraph("AMSPM", styles['Center']))
        elements.append(Spacer(1, 0.5*cm))

        # Add title
        elements.append(Paragraph("OFFERTE", styles['Center']))
        elements.append(Spacer(1, 1*cm))

        # Company info
        elements.append(Paragraph("AMSPM Security", styles['Heading3']))
        elements.append(Paragraph("Adres: Voorbeeldstraat 123", styles['Normal']))
        elements.append(Paragraph("1234 AB Amsterdam", styles['Normal']))
        elements.append(Paragraph("Telefoon: 020-1234567", styles['Normal']))
        elements.append(Paragraph("Email: <EMAIL>", styles['Normal']))
        elements.append(Paragraph("Website: www.amspm.nl", styles['Normal']))
        elements.append(Paragraph("KVK: 12345678", styles['Normal']))
        elements.append(Paragraph("BTW: NL123456789B01", styles['Normal']))
        elements.append(Spacer(1, 0.5*cm))

        # Quotation details
        elements.append(Paragraph("Offerte Details", styles['Heading3']))
        elements.append(Paragraph(f"Offerte nummer: {quotation_dict['quotation_number'] or quotation_dict['id']}", styles['Normal']))
        elements.append(Paragraph(f"Datum: {format_date(quotation_dict['created_at'])}", styles['Normal']))
        elements.append(Paragraph(f"Geldig tot: {format_date(quotation_dict['valid_until'])}", styles['Normal']))
        elements.append(Spacer(1, 0.5*cm))

        # Convert customer to dict to avoid timezone issues
        customer_dict = customer.to_dict()

        # Customer details
        elements.append(Paragraph("Klant Gegevens", styles['Heading3']))
        elements.append(Paragraph(f"Naam: {customer_dict['name']}", styles['Normal']))
        if customer_dict.get('address'):
            elements.append(Paragraph(f"Adres: {customer_dict['address']}", styles['Normal']))
        if customer_dict.get('postal_code'):
            elements.append(Paragraph(f"Postcode: {customer_dict['postal_code']}", styles['Normal']))
        if customer_dict.get('city'):
            elements.append(Paragraph(f"Plaats: {customer_dict['city']}", styles['Normal']))
        if customer_dict.get('email'):
            elements.append(Paragraph(f"Email: {customer_dict['email']}", styles['Normal']))
        if customer_dict.get('phone'):
            elements.append(Paragraph(f"Telefoon: {customer_dict['phone']}", styles['Normal']))
        elements.append(Spacer(1, 0.5*cm))

        # Introduction if available
        if quotation_dict.get('introduction'):
            elements.append(Paragraph("Inleiding", styles['Heading3']))
            elements.append(Paragraph(quotation_dict['introduction'], styles['Normal']))
            elements.append(Spacer(1, 0.5*cm))

        # Products and services
        elements.append(Paragraph("Producten en Diensten", styles['Heading3']))

        # Get items from quotation_dict
        items = quotation_dict.get('items', [])

        if items:
            # Table header
            table_data = [["Omschrijving", "Aantal", "Prijs per stuk", "Korting", "Totaal"]]

            # Add items to table
            for item in items:
                # Get the unit price and total price from the item
                unit_price = item.get('unit_price', 0)
                quantity = item.get('quantity', 1)
                discount_percentage = item.get('discount_percentage', 0)

                # Get the total price
                total_price = item.get('total_price', 0)

                # Add the item to the table
                table_data.append([
                    item.get('description', ''),
                    str(quantity),
                    f"€ {format_price(unit_price)}",
                    f"{discount_percentage}%" if discount_percentage else '-',
                    f"€ {format_price(total_price)}"
                ])

            # Create the table
            table = Table(table_data, colWidths=[8*cm, 2*cm, 3*cm, 2*cm, 3*cm])

            # Add style to the table
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))

            elements.append(table)
            elements.append(Spacer(1, 0.5*cm))

        # Totals
        totals_data = []
        totals_data.append(["Subtotaal:", f"€ {format_price(quotation_dict.get('subtotal', 0))}", ""])

        if quotation_dict.get('discount_amount', 0) > 0:
            totals_data.append([
                f"Korting ({quotation_dict.get('discount_percentage', 0)}%):",
                f"€ {format_price(quotation_dict.get('discount_amount', 0))}",
                ""
            ])

        totals_data.append(["Totaal excl. BTW:", f"€ {format_price(quotation_dict.get('total_excl_vat', 0))}", ""])
        totals_data.append([
            f"BTW ({quotation_dict.get('vat_percentage', 21)}%):",
            f"€ {format_price(quotation_dict.get('vat_amount', 0))}",
            ""
        ])
        totals_data.append(["Totaal incl. BTW:", f"€ {format_price(quotation_dict.get('total_incl_vat', 0))}", ""])

        # Create totals table
        totals_table = Table(totals_data, colWidths=[8*cm, 3*cm, 7*cm])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('FONTNAME', (0, -1), (1, -1), 'Helvetica-Bold'),
            ('LINEABOVE', (0, -1), (1, -1), 1, colors.black),
            ('LINEBELOW', (0, -1), (1, -1), 1, colors.black),
        ]))

        elements.append(totals_table)
        elements.append(Spacer(1, 1*cm))

        # Conclusion if available
        if quotation_dict.get('conclusion'):
            elements.append(Paragraph("Conclusie", styles['Heading3']))
            elements.append(Paragraph(quotation_dict['conclusion'], styles['Normal']))
            elements.append(Spacer(1, 0.5*cm))

        # Add some space before footer
        elements.append(Spacer(1, 0.5*cm))

        # Footer
        elements.append(Paragraph(f"Deze offerte is geldig tot {format_date(quotation_dict['valid_until'])}.", styles['Normal']))
        elements.append(Paragraph("Op al onze leveringen en diensten zijn onze algemene voorwaarden van toepassing.", styles['Normal']))

        # Build the PDF
        doc.build(elements)

        # Get PDF bytes and upload to Firebase Storage
        pdf_bytes = buffer.getvalue()
        buffer.close()

        # Create destination path for Firebase Storage
        destination_path = f"quotations/{quotation.customer_id}/{filename}"

        # Upload to Firebase Storage
        file_url, storage_path = upload_bytes_to_storage(
            pdf_bytes,
            destination_path,
            content_type="application/pdf"
        )

        # Zorg ervoor dat valid_until een tijdzone heeft als het bestaat
        expiry_date = None
        if quotation.valid_until:
            if quotation.valid_until.tzinfo is None:
                expiry_date = quotation.valid_until.replace(tzinfo=timezone.utc)
            else:
                expiry_date = quotation.valid_until

        # Create the document
        document = document_repo.create(
            customer_id=quotation.customer_id,
            event_id=None,
            file_url=file_url,
            file_path=storage_path,
            document_type="offerte",
            uploaded_by=quotation.created_by,
            expiry_date=expiry_date,
            related_document_id=None
        )

        return document.to_dict()

    def update_quotation_status(self, quotation_id: int, status: str) -> Dict:
        """
        Update only the status of a quotation.

        Args:
            quotation_id: Quotation ID
            status: New status

        Returns:
            Updated quotation data

        Raises:
            Exception: If quotation not found or status is invalid
        """
        # Get quotation
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            logger.error(f"Quotation not found with ID: {quotation_id}")
            raise Exception("Quotation not found")

        # Validate status
        valid_statuses = ["concept", "sent", "accepted", "rejected"]
        if status not in valid_statuses:
            logger.error(f"Invalid status: {status}. Must be one of: {', '.join(valid_statuses)}")
            raise Exception(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")

        # Check if status is changing
        if quotation.status == status:
            logger.info(f"Quotation {quotation_id} status already set to {status}")
            return quotation.to_dict()

        # Log status change
        logger.info(f"Updating quotation {quotation_id} status from {quotation.status} to {status}")

        # Update status
        quotation.status = status

        # If status is accepted or rejected, update the timestamp
        if status in ["accepted", "rejected"]:
            quotation.updated_at = datetime.now(timezone.utc)

        # If the quotation doesn't have a document yet, create one
        if not quotation.document_id:
            try:
                # Create a document for the quotation
                logger.info(f"Creating document for quotation {quotation_id}")
                document = self.generate_quotation_pdf(quotation_id)

                # Link the document to the quotation
                quotation.document_id = document["id"]
                logger.info(f"Linked document {document['id']} to quotation {quotation_id}")
            except Exception as e:
                logger.error(f"Failed to create document for quotation {quotation_id}: {str(e)}")
                # Continue with the status update even if document creation fails

        db.session.commit()

        return quotation.to_dict()



    def process_single_rejected_quotation(self, quotation_id: int) -> Dict:
        """
        Process a single rejected quotation by deleting it and its associated customer
        if the customer has no other quotations.

        Args:
            quotation_id: Quotation ID

        Returns:
            Dictionary with information about the operation
        """
        # Get the quotation
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            raise Exception("Quotation not found")

        # Check if the quotation is rejected
        if quotation.status != "rejected":
            # If not rejected, reject it first
            quotation.status = "rejected"
            db.session.commit()

        customer_id = quotation.customer_id
        customer_deleted = False

        # Check if customer has other non-rejected quotations
        other_quotations = self.quotation_repo.get_by_customer_and_not_status(
            customer_id, "rejected"
        )

        # Delete the quotation
        self.quotation_repo.delete(quotation_id)

        # If customer has no other quotations, delete the customer
        if not other_quotations:
            self.customer_repo.delete(customer_id)
            customer_deleted = True

        return {
            "quotation_deleted": True,
            "customer_deleted": customer_deleted
        }
